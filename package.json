{"name": "cupboard-static", "shortcutName": "智能柜2.0", "version": "2.0.0", "smallVersion": "", "actualVersion": "2.0.0.2024.11.26.1624", "devWebPort": 8765, "serverPort": 9527, "isCreateWindow": true, "isLoginOpen": true, "private": true, "homepage": "", "email": "", "author": "gosuncn, Inc <<EMAIL>>", "description": "cupboard static", "license": "MIT", "main": "main.js", "scripts": {"build:before": "npm run install-package-after && node ./build_before.js", "build:linux-amd64-deb": "electron-builder --dir --linux && npm run mkdeb", "build:linux-arm64-deb": "electron-builder --dir --arm64 --linux && npm run mkdeb", "build:linux-armv7l-deb": "electron-builder --dir --armv7l && npm run mkdeb", "build:server": "npm run install-package-after && node ./build_before.js --server", "build:web": "vue-cli-service build --web", "build:win-exe": "electron-builder --win", "dev:linux-amd64": "LD_LIBRARY_PATH=${LD_LIBRARY_PATH:=./extraResources/linux/x64/face_sdk:./extraResources/linux/x64/face_sdk/models/:./extraResources/linux/x64/hs_auth/} electron .", "dev:linux-arm64": "LD_LIBRARY_PATH=${LD_LIBRARY_PATH:=./extraResources/linux/arm64/face_sdk:./extraResources/linux/arm64/face_sdk/models/:./extraResources/linux/arm64/hs_auth/} electron .", "dev:linux-armv7l": "electron .", "dev:web": "vue-cli-service serve --web", "dev:win": "chcp 65001 && electron .", "install-package-after": "node ./install_package_after.js", "lint": "vue-cli-service lint", "mkdeb": "cd ./linux && sudo chmod +x ./*.sh && sed -i 's/\r$//' ./*.rules ./*.sh ./scripts/* && ./mkdeb.sh", "postinstall": "npm run install-package-after", "postuninstall": "electron-builder install-app-deps", "rebuild": "electron-rebuild"}, "build": {"appId": "cupboard.static", "productName": "CupboardStatic", "copyright": "gosuncn", "directories": {"output": "build"}, "files": ["main.js", "global.js", "hardware/**/*", "server/**/*", "dist/**/*"], "asar": true, "extraResources": [{"from": "./extraResources/", "to": "extraResources"}, {"from": "./public/icon.ico", "to": "../icon.ico"}, {"from": "./public/icon.png", "to": "../icon.png"}, {"from": "./linux/start.sh", "to": "../start.sh"}, {"from": "./appdb/", "to": "../appdb/"}], "win": {"icon": "./public/icon.ico", "artifactName": "cupboard-static_ia32_2.0.0.2024.11.26.1624.exe", "target": [{"target": "nsis", "arch": ["ia32"]}], "asarUnpack": ["node_modules/sqlite3/**"]}, "linux": {"icon": "./public/icon.png", "target": ["deb"], "asarUnpack": ["node_modules/sqlite3/**"], "category": "Utility"}, "nsis": {"oneClick": true, "allowElevation": true, "allowToChangeInstallationDirectory": false, "installerIcon": "./public/install.ico", "uninstallerIcon": "./public/install.ico", "installerHeaderIcon": "./public/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "智能柜2.0"}}, "dependencies": {"adm-zip": "^0.5.16", "archiver": "^5.2.0", "axios": "^0.18.0", "better-scroll": "^2.5.1", "core-js": "^3.8.3", "cors": "^2.8.5", "crypto-js": "^4.2.0", "electron-log": "^4.4.1", "express": "^4.19.2", "ffi-napi": "^3.0.1", "flatpickr": "^4.6.13", "gm-crypto": "^0.1.8", "gosuncn-ui": "^1.0.32", "jsencrypt-node": "^1.0.4", "jsrsasign": "^10.8.6", "jsonwebtoken": "^9.0.2", "node-fetch": "^2.7.0", "mqtt": "4.3.8", "pdf-to-printer": "^5.6.0", "qs": "^6.13.0", "ref-array-di": "^1.2.2", "ref-napi": "^3.0.1", "ref-wchar-napi": "^1.0.3", "serialport": "9.0.0", "serve-static": "^1.15.0", "sqlite3": "4.2.0", "systeminformation": "^5.21.8", "three": "^0.158.0", "uuid": "^11.0.5", "unix-print": "^1.3.2", "vue": "^2.6.14", "vue-i18n": "^8.2.1", "vue-router": "^3.5.1", "vuex": "^3.4.0", "web-ifc": "0.0.44", "web-ifc-three": "0.0.125", "ws": "^8.18.0", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "electron": "^13.6.9", "electron-builder": "^21.2.0", "@electron/rebuild": "^3.2.10", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^7.15.1", "less": "^4.1.2", "less-loader": "4.1.0", "lint-staged": "^11.1.2", "prettier": "^2.3.2", "vue-template-compiler": "^2.6.14", "webpack-spritesmith": "^1.1.0"}, "config": {"electron_mirror": "https://registry.npmmirror.com/-/binary/electron/", "electron_builder_binaries_mirror": "https://registry.npmmirror.com/-/binary/electron-builder-binaries/", "atom_mirror": "https://registry.npmmirror.com/-/binary/atom/", "sqlite3_mirror": "https://registry.npmmirror.com/-/binary/sqlite3/", "sharp_libvips_mirror": "https://registry.npmmirror.com/-/binary/electron/", "sqlite3_binary_host_mirror": "https://registry.npmmirror.com/-/binary/sharp-libvips/", "sharp_mirror": "https://registry.npmmirror.com/-/binary/sharp/", "fsevents_mirror": "https://registry.npmmirror.com/-/binary/fsevents/", "sharp_libvips_binary_host_mirror": "https://registry.npmmirror.com/binary.html?path=sharp-libvips"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*{.js,.ve}": ["eslint", "git add"]}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}